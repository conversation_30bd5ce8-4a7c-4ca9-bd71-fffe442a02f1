name: Deploy Frontend with page function

on:
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - uses: pnpm/action-setup@v3
        name: Install pnpm
        id: pnpm-install
        with:
          version: 8
          run_install: false

      - name: Deploy Frontend for ${{ github.ref_name }}
        run: |
          cd frontend/
          pnpm install --no-frozen-lockfile
          pnpm build:pages
          cd ../pages/
          echo '${{ secrets.PAGE_TOML }}' > wrangler.toml
          pnpm install --no-frozen-lockfile
          pnpm run deploy
          echo "Deploying prodcution for ${{ github.ref_name }}"
        env:
          CLOUDFLARE_ACCOUNT_ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
