# 🚀 Cloudflare Temp Email 部署指南

## ✅ 构建状态

- ✅ 后端 Worker 构建成功
- ✅ 前端 Pages 构建成功
- ✅ Linux.do OAuth2 配置已完成

## 📋 部署前准备

### 1. Cloudflare 账号设置

1. 登录 [Cloudflare Dashboard](https://dash.cloudflare.com/)
2. 获取 API Token：
   - 进入 "My Profile" → "API Tokens"
   - 创建自定义 Token，权限包括：
     - Zone:Zone:Read
     - Zone:DNS:Edit
     - Account:Cloudflare Workers:Edit
     - Account:Cloudflare Pages:Edit
     - Account:D1:Edit

### 2. 创建 D1 数据库

```bash
# 登录 Cloudflare
wrangler login

# 创建数据库
wrangler d1 create temp-email-db

# 记录返回的 database_id，例如：
# database_id = "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
```

### 3. 初始化数据库

```bash
# 使用项目中的 schema.sql 初始化数据库
wrangler d1 execute temp-email-db --file=../db/schema.sql
```

## 🔧 配置文件

### 更新 worker/wrangler.toml

编辑 `worker/wrangler.toml` 文件，更新以下配置：

```toml
name = "your-worker-name"  # 改为你的 Worker 名称
main = "src/worker.ts"
compatibility_date = "2025-04-01"
compatibility_flags = [ "nodejs_compat" ]

[vars]
PREFIX = "tmp"
DEFAULT_DOMAINS = ["your-domain.com"]  # 改为你的域名
DOMAINS = ["your-domain.com"]          # 改为你的域名
JWT_SECRET = "your-random-jwt-secret"  # 改为随机字符串
BLACK_LIST = ""
ENABLE_USER_CREATE_EMAIL = true
ENABLE_USER_DELETE_EMAIL = true
ENABLE_AUTO_REPLY = false

[[d1_databases]]
binding = "DB"
database_name = "temp-email-db"
database_id = "your-database-id-here"  # 改为实际的数据库 ID
```

## 🚀 部署步骤

### 1. 部署后端 Worker

```bash
cd worker/
pnpm run deploy
```

### 2. 部署前端 Pages

```bash
cd frontend/

# 首次部署，创建 Pages 项目
wrangler pages deploy ./dist --project-name=temp-email-frontend --branch=production

# 后续部署
wrangler pages deploy ./dist --project-name=temp-email-frontend
```

## 🌐 域名配置

### 1. Worker 自定义域名

在 `worker/wrangler.toml` 中添加：

```toml
routes = [
    { pattern = "api.your-domain.com", custom_domain = true },
]
```

### 2. Pages 自定义域名

1. 在 Cloudflare Dashboard 中进入 Pages 项目
2. 点击 "Custom domains"
3. 添加你的域名（如：temp-email.your-domain.com）

## ⚙️ OAuth2 配置

### 1. 访问管理员界面

部署完成后，访问：`https://your-domain.com/admin`

### 2. 配置 Linux.do OAuth2

在 "用户 OAuth2 设置" 中添加：

```json
{
  "name": "Linux.do",
  "clientID": "33nooFlHGB3Koqb3CUQg38cwXggMpic3",
  "clientSecret": "qxXrcbSOvAyQ2mQvGYP6joYI7bDzsqWE",
  "authorizationURL": "https://connect.linux.do/oauth2/authorize",
  "accessTokenURL": "https://connect.linux.do/oauth2/token",
  "accessTokenFormat": "urlencoded",
  "userInfoURL": "https://connect.linux.do/api/user",
  "redirectURL": "https://your-domain.com/user/oauth2/callback",
  "userEmailKey": "username",
  "scope": "read",
  "enableMailAllowList": false,
  "mailAllowList": ["linux.do"]
}
```

## 🔍 验证部署

### 1. 检查 Worker

访问：`https://your-worker-name.your-account.workers.dev/api/health`

### 2. 检查前端

访问：`https://your-pages-domain.pages.dev`

### 3. 测试 OAuth2

1. 访问登录页面
2. 点击 "使用 Linux.do 登录"
3. 完成授权流程

## 🛠️ 故障排除

### 常见问题

1. **Worker 部署失败**
   - 检查 wrangler.toml 配置
   - 确认 D1 数据库 ID 正确

2. **Pages 部署失败**
   - 检查构建产物是否存在：`frontend/dist/`
   - 确认 wrangler 权限

3. **OAuth2 登录失败**
   - 检查回调 URL 配置
   - 确认 Client ID/Secret 正确

### 日志查看

```bash
# Worker 日志
wrangler tail your-worker-name

# Pages 部署日志
wrangler pages deployment list --project-name=temp-email-frontend
```

## 📚 相关文档

- [Cloudflare Workers 文档](https://developers.cloudflare.com/workers/)
- [Cloudflare Pages 文档](https://developers.cloudflare.com/pages/)
- [Cloudflare D1 文档](https://developers.cloudflare.com/d1/)
- [Linux.do OAuth2 配置](./LINUX_DO_OAUTH2_SETUP.md)

---

🎉 部署完成后，你的临时邮箱服务就可以正常使用了！
