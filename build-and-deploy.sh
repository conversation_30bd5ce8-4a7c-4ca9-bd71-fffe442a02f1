#!/bin/bash

# Cloudflare Temp Email 构建和部署脚本
# 使用方法: ./build-and-deploy.sh

set -e  # 遇到错误立即退出

echo "🚀 开始构建 Cloudflare Temp Email 项目..."

# 检查必要的工具
echo "📋 检查必要工具..."
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js"
    exit 1
fi

if ! command -v pnpm &> /dev/null; then
    echo "❌ pnpm 未安装，正在安装..."
    npm install -g pnpm
fi

if ! command -v wrangler &> /dev/null; then
    echo "❌ wrangler 未安装，正在安装..."
    npm install -g wrangler
fi

echo "✅ 工具检查完成"

# 检查 Cloudflare 认证
echo "🔐 检查 Cloudflare 认证..."
if ! wrangler whoami &> /dev/null; then
    echo "❌ 未登录 Cloudflare，请先运行: wrangler login"
    echo "或者设置环境变量:"
    echo "export CLOUDFLARE_API_TOKEN=your_api_token"
    echo "export CLOUDFLARE_ACCOUNT_ID=your_account_id"
    exit 1
fi

echo "✅ Cloudflare 认证检查完成"

# 创建基本的 wrangler.toml 配置
echo "📝 创建 wrangler.toml 配置..."
if [ ! -f "worker/wrangler.toml" ]; then
    echo "⚠️  未找到 worker/wrangler.toml，请手动创建或提供配置"
    echo "你可以基于 worker/wrangler.toml.template 创建配置文件"
    echo ""
    echo "最小配置示例:"
    echo "name = \"your-worker-name\""
    echo "main = \"src/worker.ts\""
    echo "compatibility_date = \"2025-04-01\""
    echo "compatibility_flags = [ \"nodejs_compat\" ]"
    echo ""
    echo "[[d1_databases]]"
    echo "binding = \"DB\""
    echo "database_name = \"your-database-name\""
    echo "database_id = \"your-database-id\""
    echo ""
    read -p "是否要创建一个基本的配置文件? (y/n): " create_config
    if [ "$create_config" = "y" ]; then
        read -p "请输入 Worker 名称: " worker_name
        read -p "请输入 D1 数据库名称: " db_name
        read -p "请输入 D1 数据库 ID: " db_id
        
        cat > worker/wrangler.toml << EOF
name = "$worker_name"
main = "src/worker.ts"
compatibility_date = "2025-04-01"
compatibility_flags = [ "nodejs_compat" ]

[vars]
PREFIX = "tmp"
DEFAULT_DOMAINS = ["example.com"]
DOMAINS = ["example.com"]
JWT_SECRET = "your-jwt-secret-change-this"
BLACK_LIST = ""
ENABLE_USER_CREATE_EMAIL = true
ENABLE_USER_DELETE_EMAIL = true
ENABLE_AUTO_REPLY = false

[[d1_databases]]
binding = "DB"
database_name = "$db_name"
database_id = "$db_id"
EOF
        echo "✅ 基本配置文件已创建，请根据需要修改 worker/wrangler.toml"
    else
        echo "❌ 请先创建 worker/wrangler.toml 配置文件"
        exit 1
    fi
fi

# 构建后端 (Worker)
echo "🔨 构建后端 Worker..."
cd worker/

echo "📦 安装后端依赖..."
pnpm install --no-frozen-lockfile

echo "🏗️  构建 Worker..."
pnpm run build

echo "✅ 后端构建完成"
cd ..

# 构建前端
echo "🔨 构建前端..."
cd frontend/

echo "📦 安装前端依赖..."
pnpm install --no-frozen-lockfile

echo "🏗️  构建前端 (Pages 模式)..."
pnpm run build:pages

echo "✅ 前端构建完成"
cd ..

echo "🎉 构建完成！"
echo ""
echo "📁 构建产物:"
echo "  - 后端: worker/dist/"
echo "  - 前端: frontend/dist/"
echo ""
echo "🚀 部署说明:"
echo "1. 后端部署: cd worker && pnpm run deploy"
echo "2. 前端部署: cd frontend && wrangler pages deploy ./dist --project-name=your-project-name"
echo ""
echo "⚠️  部署前请确保:"
echo "1. 已创建 D1 数据库并运行了初始化 SQL"
echo "2. 已配置正确的域名和环境变量"
echo "3. 已设置 Cloudflare API 凭据"
