#!/bin/bash

# Cloudflare Temp Email 完整部署脚本
# 使用方法: ./deploy-to-cloudflare.sh

set -e

echo "🚀 Cloudflare Temp Email 完整部署脚本"
echo "=================================="

# 检查是否已登录 Cloudflare
check_cloudflare_auth() {
    echo "🔐 检查 Cloudflare 认证状态..."
    if ! wrangler whoami &> /dev/null; then
        echo "❌ 未登录 Cloudflare"
        echo "请选择认证方式:"
        echo "1. 使用浏览器登录: wrangler login"
        echo "2. 使用 API Token (推荐):"
        echo "   export CLOUDFLARE_API_TOKEN=your_api_token"
        echo "   export CLOUDFLARE_ACCOUNT_ID=your_account_id"
        echo ""
        read -p "是否现在登录? (y/n): " do_login
        if [ "$do_login" = "y" ]; then
            wrangler login
        else
            echo "❌ 请先完成 Cloudflare 认证"
            exit 1
        fi
    else
        echo "✅ Cloudflare 认证成功"
        wrangler whoami
    fi
}

# 创建 D1 数据库
create_d1_database() {
    echo "🗄️  创建 D1 数据库..."
    read -p "请输入数据库名称 (例如: temp-email-db): " db_name
    
    echo "正在创建数据库: $db_name"
    db_output=$(wrangler d1 create "$db_name" 2>&1)
    
    if echo "$db_output" | grep -q "database_id"; then
        db_id=$(echo "$db_output" | grep "database_id" | sed 's/.*database_id = "\([^"]*\)".*/\1/')
        echo "✅ 数据库创建成功"
        echo "数据库名称: $db_name"
        echo "数据库 ID: $db_id"
        
        # 初始化数据库
        echo "📊 初始化数据库结构..."
        if [ -f "db/schema.sql" ]; then
            wrangler d1 execute "$db_name" --file=db/schema.sql
            echo "✅ 数据库结构初始化完成"
        else
            echo "⚠️  未找到 db/schema.sql，请手动初始化数据库"
        fi
        
        return 0
    else
        echo "❌ 数据库创建失败: $db_output"
        return 1
    fi
}

# 配置 Worker
configure_worker() {
    echo "⚙️  配置 Worker..."
    
    if [ ! -f "worker/wrangler.toml" ]; then
        echo "📝 创建 Worker 配置..."
        read -p "请输入 Worker 名称 (例如: temp-email-worker): " worker_name
        read -p "请输入主域名 (例如: temp-email.example.com): " main_domain
        read -p "请输入 JWT 密钥 (随机字符串): " jwt_secret
        
        # 如果没有提供数据库信息，尝试创建
        if [ -z "$db_name" ] || [ -z "$db_id" ]; then
            echo "需要 D1 数据库信息..."
            create_d1_database
        fi
        
        cat > worker/wrangler.toml << EOF
name = "$worker_name"
main = "src/worker.ts"
compatibility_date = "2025-04-01"
compatibility_flags = [ "nodejs_compat" ]

[vars]
PREFIX = "tmp"
DEFAULT_DOMAINS = ["$main_domain"]
DOMAINS = ["$main_domain"]
JWT_SECRET = "$jwt_secret"
BLACK_LIST = ""
ENABLE_USER_CREATE_EMAIL = true
ENABLE_USER_DELETE_EMAIL = true
ENABLE_AUTO_REPLY = false

[[d1_databases]]
binding = "DB"
database_name = "$db_name"
database_id = "$db_id"
EOF
        echo "✅ Worker 配置创建完成"
    else
        echo "✅ 使用现有的 Worker 配置"
    fi
}

# 部署 Worker
deploy_worker() {
    echo "🚀 部署 Worker..."
    cd worker/
    
    echo "📦 安装依赖..."
    pnpm install --no-frozen-lockfile
    
    echo "🚀 部署到 Cloudflare Workers..."
    pnpm run deploy
    
    echo "✅ Worker 部署完成"
    cd ..
}

# 部署前端
deploy_frontend() {
    echo "🌐 部署前端..."
    cd frontend/
    
    echo "📦 安装依赖..."
    pnpm install --no-frozen-lockfile
    
    read -p "请输入 Pages 项目名称 (例如: temp-email-frontend): " pages_name
    
    echo "🏗️  构建前端..."
    pnpm run build:pages
    
    echo "🚀 部署到 Cloudflare Pages..."
    wrangler pages deploy ./dist --project-name="$pages_name" --branch=production
    
    echo "✅ 前端部署完成"
    cd ..
}

# 主流程
main() {
    echo "开始部署流程..."
    
    # 检查认证
    check_cloudflare_auth
    
    # 配置 Worker
    configure_worker
    
    # 部署 Worker
    deploy_worker
    
    # 部署前端
    deploy_frontend
    
    echo ""
    echo "🎉 部署完成！"
    echo "=================================="
    echo "📋 部署信息:"
    echo "  - Worker: 已部署到 Cloudflare Workers"
    echo "  - 前端: 已部署到 Cloudflare Pages"
    echo ""
    echo "🔧 后续配置:"
    echo "1. 在 Cloudflare Pages 中配置自定义域名"
    echo "2. 在 Worker 中配置环境变量"
    echo "3. 配置 DNS 记录指向你的服务"
    echo "4. 在管理员界面中配置 Linux.do OAuth2"
    echo ""
    echo "📖 更多信息请查看 LINUX_DO_OAUTH2_SETUP.md"
}

# 运行主流程
main
