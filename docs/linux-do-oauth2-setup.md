# Linux.do OAuth2 配置指南

本指南将帮助你配置 Linux.do OAuth2 登录功能。

## 前提条件

1. 你的应用已经部署并可以正常访问
2. 你有管理员权限访问应用的后台管理界面
3. 你已经在 Linux.do 创建了 OAuth2 应用

## Linux.do OAuth2 信息

根据你提供的信息：

- **Client ID**: `33nooFlHGB3Koqb3CUQg38cwXggMpic3`
- **Client Secret**: `qxXrcbSOvAyQ2mQvGYP6joYI7bDzsqWE`
- **授权端点**: `https://connect.linux.do/oauth2/authorize`
- **Token 端点**: `https://connect.linux.do/oauth2/token`
- **用户信息端点**: `https://connect.linux.do/api/user`

## 配置步骤

### 1. 登录管理员界面

访问你的应用管理员界面，通常是 `https://your-domain.com/admin`

### 2. 进入 OAuth2 设置

在管理员界面中找到 "用户 OAuth2 设置" 页面

### 3. 添加 Linux.do OAuth2 配置

点击 "添加 Oauth2" 按钮，选择 "Linux.do" 类型，然后填入以下配置：

```json
{
  "name": "Linux.do",
  "clientID": "33nooFlHGB3Koqb3CUQg38cwXggMpic3",
  "clientSecret": "qxXrcbSOvAyQ2mQvGYP6joYI7bDzsqWE",
  "authorizationURL": "https://connect.linux.do/oauth2/authorize",
  "accessTokenURL": "https://connect.linux.do/oauth2/token",
  "accessTokenFormat": "urlencoded",
  "userInfoURL": "https://connect.linux.do/api/user",
  "redirectURL": "https://your-domain.com/user/oauth2/callback",
  "userEmailKey": "username",
  "scope": "read",
  "enableMailAllowList": false,
  "mailAllowList": ["linux.do"]
}
```

**重要**: 请将 `https://your-domain.com` 替换为你的实际域名。

### 4. 配置回调 URL

确保在 Linux.do 的应用设置中配置正确的回调 URL：
```
https://your-domain.com/user/oauth2/callback
```

### 5. 保存配置

点击 "保存" 按钮保存配置。

## 用户登录流程

配置完成后，用户可以：

1. 访问登录页面
2. 点击 "使用 Linux.do 登录" 按钮
3. 跳转到 Linux.do 授权页面
4. 授权后自动跳转回应用并完成登录

## 特殊说明

### 虚拟邮箱地址

由于 Linux.do 返回的用户信息中没有 email 字段，系统会自动为用户生成虚拟邮箱地址：
```
<EMAIL>
```

例如，如果用户的 Linux.do 用户名是 `testuser`，那么在系统中的邮箱地址将是 `<EMAIL>`。

### 用户信息映射

Linux.do 返回的用户信息包含：
- `id`: 用户唯一标识符（不会改变）
- `username`: 用户名（可能会改变）
- `name`: 用户昵称
- `trust_level`: 信任等级（0-4）

系统使用 `username` 字段来生成虚拟邮箱地址。

## 故障排除

### 常见问题

1. **回调 URL 不匹配**
   - 确保 Linux.do 应用设置中的回调 URL 与配置中的 `redirectURL` 完全一致

2. **Client ID 或 Secret 错误**
   - 检查配置中的 Client ID 和 Secret 是否正确

3. **用户无法登录**
   - 检查应用是否已正确部署
   - 确认 OAuth2 配置已保存
   - 查看浏览器开发者工具的网络请求是否有错误

### 调试信息

如果遇到问题，可以查看：
1. 浏览器开发者工具的控制台输出
2. 网络请求的响应内容
3. 应用的日志输出

## 安全建议

1. 定期更新 Client Secret
2. 确保应用使用 HTTPS
3. 监控异常登录活动
4. 定期备份用户数据
