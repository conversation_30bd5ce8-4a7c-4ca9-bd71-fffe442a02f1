<script setup>
import { defineAsyncComponent, onMounted, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'

import { useGlobalState } from '../store'
import { api } from '../api'

import AddressBar from './index/AddressBar.vue';
import MailBox from '../components/MailBox.vue';
import SendBox from '../components/SendBox.vue';
import AutoReply from './index/AutoReply.vue';
import AccountSettings from './index/AccountSettings.vue';
import Webhook from './index/Webhook.vue';
import Attachment from './index/Attachment.vue';
import About from './common/About.vue';

const { loading, settings, openSettings, indexTab, globalTabplacement } = useGlobalState()
const message = useMessage()
const route = useRoute()

const SendMail = defineAsyncComponent(() => {
  loading.value = true;
  return import('./index/SendMail.vue')
    .finally(() => loading.value = false);
});

const { t } = useI18n({
  messages: {
    en: {
      mailbox: 'Mail Box',
      sendbox: 'Send Box',
      sendmail: 'Send Mail',
      auto_reply: 'Auto Reply',
      accountSettings: 'Account Settings',
      about: 'About',
      s3Attachment: 'S3 Attachment',
      saveToS3Success: 'save to s3 success',
      webhookSettings: 'Webhook Settings',
      query: 'Query',
    },
    zh: {
      mailbox: '收件箱',
      sendbox: '发件箱',
      sendmail: '发送邮件',
      auto_reply: '自动回复',
      accountSettings: '账户设置',
      about: '关于',
      s3Attachment: 'S3附件',
      saveToS3Success: '保存到s3成功',
      webhookSettings: 'Webhook 设置',
      query: '查询',
    }
  }
});

const fetchMailData = async (limit, offset) => {
  // 使用安全的用户API，需要用户登录验证
  if (mailIdQuery.value > 0) {
    // 注意：单个邮件查询功能暂时禁用，因为用户API不支持按ID查询单个邮件
    // 如果需要此功能，需要在后端添加相应的用户API
    return { results: [], count: 0 };
  }
  return await api.fetch(`/user_api/mails?limit=${limit}&offset=${offset}`);
};

const deleteMail = async (curMailId) => {
  // 使用安全的用户API，需要用户登录验证
  await api.fetch(`/user_api/mails/${curMailId}`, { method: 'DELETE' });
};

const deleteSenboxMail = async (curMailId) => {
  // 发件功能已被禁用
  throw new Error("发件功能已被禁用");
};

const fetchSenboxData = async (limit, offset) => {
  // 发件功能已被禁用
  throw new Error("发件功能已被禁用");
};

const saveToS3 = async (mail_id, filename, blob) => {
  // S3附件功能已被禁用，因为依赖JWT验证
  message.error("S3附件功能已被禁用");
}

const mailBoxKey = ref("")
const mailIdQuery = ref("")
const showMailIdQuery = ref(false)

const queryMail = () => {
  mailBoxKey.value = Date.now();
}

watch(route, () => {
  if (!route.query.mail_id) {
    showMailIdQuery.value = false;
    mailIdQuery.value = "";
    queryMail();
  }
})

onMounted(() => {
  if (route.query.mail_id) {
    showMailIdQuery.value = true;
    mailIdQuery.value = route.query.mail_id;
    queryMail();
  }
})
</script>

<template>
  <div>
    <AddressBar />
    <n-tabs v-if="settings.address" type="card" v-model:value="indexTab" :placement="globalTabplacement">
      <n-tab-pane name="mailbox" :tab="t('mailbox')">
        <div v-if="showMailIdQuery" style="margin-bottom: 10px;">
          <n-input-group>
            <n-input v-model:value="mailIdQuery" />
            <n-button @click="queryMail" type="primary" tertiary>
              {{ t('query') }}
            </n-button>
          </n-input-group>
        </div>
        <MailBox :key="mailBoxKey" :showEMailTo="false" :showReply="false" :showSaveS3="false"
          :saveToS3="saveToS3" :enableUserDeleteEmail="openSettings.enableUserDeleteEmail"
          :fetchMailData="fetchMailData" :deleteMail="deleteMail" />
      </n-tab-pane>
      <!-- 发件功能已被禁用 -->
      <n-tab-pane name="accountSettings" :tab="t('accountSettings')">
        <AccountSettings />
      </n-tab-pane>
      <!-- 自动回复、Webhook和S3附件功能已被禁用，因为依赖JWT验证 -->
      <n-tab-pane v-if="openSettings.enableIndexAbout" name="about" :tab="t('about')">
        <About />
      </n-tab-pane>
    </n-tabs>
  </div>
</template>
