<script setup>
import { ref, h, onMounted, watch } from 'vue';
import { NBadge, NSelect, useMessage } from 'naive-ui'
import { useI18n } from 'vue-i18n'

import { useGlobalState } from '../../store'
import { api } from '../../api'
import { NButton, NMenu } from 'naive-ui';
import { MenuFilled } from '@vicons/material'

const {
    loading, adminTab,
    adminMailTabAddress, adminSendBoxTabAddress
} = useGlobalState()
const message = useMessage()

const { t } = useI18n({
    messages: {
        en: {
            name: 'Name',
            created_at: 'Created At',
            updated_at: 'Update At',
            mail_count: 'Mail Count',
            send_count: 'Send Count',
            showCredential: 'Show Mail Address Credential',
            addressCredential: 'Mail Address Credential',
            addressCredentialTip: 'Please copy the Mail Address Credential and you can use it to login to your email account.',
            delete: 'Delete',
            deleteTip: 'Are you sure to delete this email?',
            delteAccount: 'Delete Account',
            viewMails: 'View Mails',
            viewSendBox: 'View SendBox',
            itemCount: 'itemCount',
            query: 'Query',
            addressQueryTip: 'Leave blank to query all addresses',
            actions: 'Actions'
        },
        zh: {
            name: '邮箱名称',
            owner: '所属用户',
            created_at: '创建时间',
            updated_at: '更新时间',
            mail_count: '邮件数量',
            send_count: '发送数量',
            showCredential: '查看邮箱地址凭证',
            addressCredential: '邮箱地址凭证',
            addressCredentialTip: '请复制邮箱地址凭证，你可以使用它登录你的邮箱。',
            delete: '删除',
            deleteTip: '确定要删除这个邮箱吗？',
            delteAccount: '删除邮箱',
            viewMails: '查看邮件',
            viewSendBox: '查看发件箱',
            itemCount: '总数',
            query: '查询',
            addressQueryTip: '留空查询所有地址',
            actions: '操作',
            assignAddress: '分配邮箱',
            unassignAddress: '取消分配',
            selectUser: '选择用户',
            assignSuccess: '分配成功',
            unassignSuccess: '取消分配成功',
            unassigned: '(未分配)',
        }
    }
});

const showEmailCredential = ref(false)
const curEmailCredential = ref("")
const curDeleteAddressId = ref(0);

const addressQuery = ref("")

const data = ref([])
const count = ref(0)
const page = ref(1)
const pageSize = ref(20)
const showDeleteAccount = ref(false)

// 邮箱分配相关
const showAssignModal = ref(false)
const curAssignAddressId = ref(0)
const curAssignAddressName = ref("")
const selectedUserId = ref(null)
const usersList = ref([])
const showUnassignModal = ref(false)
const curUnassignAddressId = ref(0)
const curUnassignUserId = ref(0)

const showCredential = async (id) => {
    // JWT邮箱凭证系统已被禁用
    message.error("JWT邮箱凭证系统已被禁用，邮箱已永久绑定到用户账户");
    showEmailCredential.value = false
    curEmailCredential.value = ""
}

const deleteEmail = async () => {
    try {
        await api.adminDeleteAddress(curDeleteAddressId.value)
        message.success("success");
        await fetchData()
    } catch (error) {
        message.error(error.message || "error");
    } finally {
        showDeleteAccount.value = false
    }
}

// 获取用户列表
const fetchUsersList = async () => {
    try {
        const users = await api.fetch('/admin/users_list');
        usersList.value = users.map(user => ({
            label: user.user_email,
            value: user.id
        }));
    } catch (error) {
        console.error('Failed to fetch users list:', error);
    }
}

// 分配邮箱
const assignAddress = async () => {
    if (!selectedUserId.value) {
        message.error('请选择用户');
        return;
    }
    try {
        await api.fetch('/admin/assign_address', {
            method: 'POST',
            body: JSON.stringify({
                addressId: curAssignAddressId.value,
                userId: selectedUserId.value
            })
        });
        message.success(t('assignSuccess'));
        await fetchData();
        showAssignModal.value = false;
        selectedUserId.value = null;
    } catch (error) {
        message.error(error.message || "error");
    }
}

// 取消分配邮箱
const unassignAddress = async () => {
    try {
        await api.fetch('/admin/unassign_address', {
            method: 'DELETE',
            body: JSON.stringify({
                addressId: curUnassignAddressId.value,
                userId: curUnassignUserId.value
            })
        });
        message.success(t('unassignSuccess'));
        await fetchData();
        showUnassignModal.value = false;
    } catch (error) {
        message.error(error.message || "error");
    }
}

const fetchData = async () => {
    try {
        addressQuery.value = addressQuery.value.trim()
        const { results, count: addressCount } = await api.fetch(
            `/admin/address`
            + `?limit=${pageSize.value}`
            + `&offset=${(page.value - 1) * pageSize.value}`
            + (addressQuery.value ? `&query=${addressQuery.value}` : "")
        );
        data.value = results;
        if (addressCount > 0) {
            count.value = addressCount;
        }
    } catch (error) {
        console.log(error)
        message.error(error.message || "error");
    }
}

const columns = [
    {
        title: "ID",
        key: "id"
    },
    {
        title: t('name'),
        key: "name"
    },
    {
        title: t('owner'),
        key: "owner_email",
        render(row) {
            return row.owner_email || t('unassigned')
        }
    },
    {
        title: t('created_at'),
        key: "created_at"
    },
    {
        title: t('updated_at'),
        key: "updated_at"
    },
    {
        title: t('mail_count'),
        key: "mail_count",
        render(row) {
            return h(NButton,
                {
                    text: true,
                    onClick: () => {
                        if (row.mail_count > 0) {
                            adminMailTabAddress.value = row.name;
                            adminTab.value = "mails";
                        }
                    }
                },
                {
                    icon: () => h(NBadge, {
                        value: row.mail_count,
                        'show-zero': true,
                        max: 99,
                        type: "success"
                    }),
                    default: () => row.mail_count > 0 ? t('viewMails') : ""
                }
            )
        }
    },
    {
        title: t('send_count'),
        key: "send_count",
        render(row) {
            return h(NButton,
                {
                    text: true,
                    onClick: () => {
                        if (row.send_count > 0) {
                            adminSendBoxTabAddress.value = row.name;
                            adminTab.value = "sendBox";
                        }
                    }
                },
                {
                    icon: () => h(NBadge, {
                        value: row.send_count,
                        'show-zero': true,
                        max: 99,
                        type: "success"
                    }),
                    default: () => row.send_count > 0 ? t('viewSendBox') : ""
                }
            )
        }
    },
    {
        title: t('actions'),
        key: 'actions',
        render(row) {
            return h('div', [
                h(NMenu, {
                    mode: "horizontal",
                    options: [
                        {
                            label: t('actions'),
                            icon: () => h(MenuFilled),
                            key: "action",
                            children: [
                                {
                                    label: () => h(NButton,
                                        {
                                            text: true,
                                            onClick: () => showCredential(row.id)
                                        },
                                        { default: () => t('showCredential') }
                                    ),
                                },
                                {
                                    label: () => h(NButton,
                                        {
                                            text: true,
                                            onClick: () => {
                                                adminMailTabAddress.value = row.name;
                                                adminTab.value = "mails";
                                            }
                                        },
                                        { default: () => t('viewMails') }
                                    ),
                                    show: row.mail_count > 0
                                },
                                {
                                    label: () => h(NButton,
                                        {
                                            text: true,
                                            onClick: () => {
                                                adminSendBoxTabAddress.value = row.name;
                                                adminTab.value = "sendBox";
                                            }
                                        },
                                        { default: () => t('viewSendBox') }
                                    ),
                                    show: row.send_count > 0
                                },
                                {
                                    label: () => h(NButton,
                                        {
                                            text: true,
                                            onClick: () => {
                                                curAssignAddressId.value = row.id;
                                                curAssignAddressName.value = row.name;
                                                showAssignModal.value = true;
                                                fetchUsersList();
                                            }
                                        },
                                        { default: () => t('assignAddress') }
                                    ),
                                    show: !row.owner_email
                                },
                                {
                                    label: () => h(NButton,
                                        {
                                            text: true,
                                            onClick: () => {
                                                curUnassignAddressId.value = row.id;
                                                curUnassignUserId.value = row.owner_id;
                                                showUnassignModal.value = true;
                                            }
                                        },
                                        { default: () => t('unassignAddress') }
                                    ),
                                    show: !!row.owner_email
                                },
                                {
                                    label: () => h(NButton,
                                        {
                                            text: true,
                                            onClick: () => {
                                                curDeleteAddressId.value = row.id;
                                                showDeleteAccount.value = true;
                                            }
                                        },
                                        { default: () => t('delete') }
                                    )
                                }
                            ]
                        }
                    ]
                })
            ])
        }
    }
]

watch([page, pageSize], async () => {
    await fetchData()
})

onMounted(async () => {
    await fetchData()
    await fetchUsersList()
})
</script>

<template>
    <div style="margin-top: 10px;">
        <n-modal v-model:show="showEmailCredential" preset="dialog" title="Dialog">
            <template #header>
                <div>{{ t("addressCredential") }}</div>
            </template>
            <span>
                <p>{{ t("addressCredentialTip") }}</p>
            </span>
            <n-card :bordered="false" embedded>
                <b>{{ curEmailCredential }}</b>
            </n-card>
            <template #action>
            </template>
        </n-modal>
        <n-modal v-model:show="showDeleteAccount" preset="dialog" :title="t('delteAccount')">
            <p>{{ t('deleteTip') }}</p>
            <template #action>
                <n-button :loading="loading" @click="deleteEmail" size="small" tertiary type="error">
                    {{ t('delteAccount') }}
                </n-button>
            </template>
        </n-modal>

        <!-- 分配邮箱模态框 -->
        <n-modal v-model:show="showAssignModal" preset="dialog" :title="t('assignAddress')">
            <p>为邮箱 <strong>{{ curAssignAddressName }}</strong> 分配用户：</p>
            <n-select
                v-model:value="selectedUserId"
                :options="usersList"
                :placeholder="t('selectUser')"
                clearable
                filterable
            />
            <template #action>
                <n-button @click="showAssignModal = false" size="small">
                    取消
                </n-button>
                <n-button :loading="loading" @click="assignAddress" size="small" type="primary">
                    确认分配
                </n-button>
            </template>
        </n-modal>

        <!-- 取消分配邮箱模态框 -->
        <n-modal v-model:show="showUnassignModal" preset="dialog" :title="t('unassignAddress')">
            <p>确定要取消此邮箱的用户分配吗？</p>
            <template #action>
                <n-button @click="showUnassignModal = false" size="small">
                    取消
                </n-button>
                <n-button :loading="loading" @click="unassignAddress" size="small" type="warning">
                    确认取消分配
                </n-button>
            </template>
        </n-modal>
        <n-input-group>
            <n-input v-model:value="addressQuery" clearable :placeholder="t('addressQueryTip')"
                @keydown.enter="fetchData" />
            <n-button @click="fetchData" type="primary" tertiary>
                {{ t('query') }}
            </n-button>
        </n-input-group>
        <div style="overflow: auto;">
            <div style="display: inline-block;">
                <n-pagination v-model:page="page" v-model:page-size="pageSize" :item-count="count"
                    :page-sizes="[20, 50, 100]" show-size-picker>
                    <template #prefix="{ itemCount }">
                        {{ t('itemCount') }}: {{ itemCount }}
                    </template>
                </n-pagination>
            </div>
            <n-data-table :columns="columns" :data="data" :bordered="false" embedded />
        </div>
    </div>
</template>

<style scoped>
.n-pagination {
    margin-top: 10px;
    margin-bottom: 10px;
}

.n-data-table {
    min-width: 1000px;
}
</style>
