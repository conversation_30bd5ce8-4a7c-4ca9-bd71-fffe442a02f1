<script setup>
import { onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n'
import { useMessage, NText } from 'naive-ui'

import { useGlobalState } from '../../store'
import { api } from '../../api'

const {
    loading, openSettings,
} = useGlobalState()
const message = useMessage()

const { t } = useI18n({
    messages: {
        en: {
            address: 'Address',
            enablePrefix: 'If enable Prefix',
            creatNewEmail: 'Get New Email',
            fillInAllFields: 'Please fill in all fields',
            successTip: 'Success Created',
            createResult: 'Create Result',
        },
        zh: {
            address: '地址',
            enablePrefix: '是否启用前缀',
            creatNewEmail: '创建新邮箱',
            fillInAllFields: '请填写完整信息',
            successTip: '创建成功',
            createResult: '创建结果',
            assignToUser: '分配给用户',
            selectUser: '选择用户（可选）',
            assignToUserTip: '可选择将邮箱直接分配给指定用户',
        }
    }
});

const enablePrefix = ref(true)
const emailName = ref("")
const emailDomain = ref("")
const showReultModal = ref(false)
const result = ref("")
const selectedUserId = ref(null)
const usersList = ref([])

// 获取用户列表
const fetchUsersList = async () => {
    try {
        const users = await api.fetch('/admin/users_list');
        usersList.value = [
            { label: '不分配给任何用户', value: null },
            ...users.map(user => ({
                label: user.user_email,
                value: user.id
            }))
        ];
    } catch (error) {
        console.error('Failed to fetch users list:', error);
    }
}

const newEmail = async () => {
    if (!emailName.value || !emailDomain.value) {
        message.error(t('fillInAllFields'))
        return
    }
    try {
        const requestBody = {
            enablePrefix: enablePrefix.value,
            name: emailName.value,
            domain: emailDomain.value,
        };

        // 如果选择了用户，添加到请求中
        if (selectedUserId.value) {
            requestBody.assignToUserId = selectedUserId.value;
        }

        const res = await api.fetch(`/admin/new_address`, {
            method: 'POST',
            body: JSON.stringify(requestBody)
        })
        // 新的API不再返回JWT，而是返回邮箱地址
        result.value = res["address"] || "邮箱创建成功";
        if (selectedUserId.value) {
            const selectedUser = usersList.value.find(u => u.value === selectedUserId.value);
            result.value += `\n已分配给用户: ${selectedUser?.label}`;
        }
        message.success(t('successTip'))
        showReultModal.value = true

        // 重置表单
        emailName.value = "";
        selectedUserId.value = null;
    } catch (error) {
        message.error(error.message || "error");
    }
}

onMounted(async () => {
    if (openSettings.prefix) {
        enablePrefix.value = true
    }
    emailDomain.value = openSettings.value.domains?.[0]?.value || ""
    await fetchUsersList()
})
</script>

<template>
    <div class="center">
        <n-modal v-model:show="showReultModal" preset="dialog" :title="t('createResult')">
            <p>{{ t('createResult') }}</p>
            <n-card :bordered="false" embedded>
                <b>{{ result }}</b>
            </n-card>
        </n-modal>
        <n-card :bordered="false" embedded style="max-width: 600px;">
            <n-form-item-row v-if="openSettings.prefix" :label="t('enablePrefix')">
                <n-checkbox v-model:checked="enablePrefix" />
            </n-form-item-row>
            <n-form-item-row :label="t('address')">
                <n-input-group>
                    <n-input-group-label v-if="enablePrefix && openSettings.prefix">
                        {{ openSettings.prefix }}
                    </n-input-group-label>
                    <n-input v-model:value="emailName" />
                    <n-input-group-label>@</n-input-group-label>
                    <n-select v-model:value="emailDomain" :consistent-menu-width="false"
                        :options="openSettings.domains" />
                </n-input-group>
            </n-form-item-row>
            <n-form-item-row :label="t('assignToUser')">
                <n-select
                    v-model:value="selectedUserId"
                    :options="usersList"
                    :placeholder="t('selectUser')"
                    clearable
                    filterable
                />
                <template #feedback>
                    <n-text depth="3" style="font-size: 12px;">
                        {{ t('assignToUserTip') }}
                    </n-text>
                </template>
            </n-form-item-row>
            <n-button @click="newEmail" type="primary" block :loading="loading">
                {{ t('creatNewEmail') }}
            </n-button>
        </n-card>
    </div>
</template>

<style scoped>
.center {
    display: flex;
    text-align: left;
    place-items: center;
    justify-content: center;
    margin: 20px;
}
</style>
