<script setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'

import { useGlobalState } from '../../store'
import { api } from '../../api'
import Appearance from '../common/Appearance.vue'
import { getRouterPathWithLang } from '../../utils'

const {
    jwt, settings, showAddressCredential, loading
} = useGlobalState()
const router = useRouter()
const message = useMessage()

const showDelteAccount = ref(false)
const { locale, t } = useI18n({
    messages: {
        en: {
            logout: "Logout",
            delteAccount: "Delete Account",
            showAddressCredential: 'Show Address Credential',
            logoutConfirm: 'Are you sure to logout?',
            delteAccount: "Delete Account",
            delteAccountConfirm: "Are you sure to delete your account and all emails for this account?",
        },
        zh: {
            logout: '退出登录',
            delteAccount: "删除账户",
            showAddressCredential: '查看邮箱地址凭证',
            logoutConfirm: '确定要退出登录吗？',
            delteAccount: "删除账户",
            delteAccountConfirm: "确定要删除你的账户和其中的所有邮件吗?",
        }
    }
});

// logout 函数已移除，退出登录功能在用户界面操作

const deleteAccount = async () => {
    // 邮箱已永久绑定到用户账户，无法删除
    message.error("邮箱已永久绑定到您的账户，无法删除");
};
</script>

<template>
    <div class="center" v-if="settings.address">
        <n-card :bordered="false" embedded>
            <Appearance />
            <!-- JWT邮箱凭证系统已被禁用，不再显示此按钮 -->
            <!-- 退出登录功能移至用户界面操作 -->
            <!-- 邮箱已永久绑定，不允许删除账户 -->
        </n-card>
        <!-- 退出登录模态框已移除 -->
        <n-modal v-model:show="showDelteAccount" preset="dialog" :title="t('delteAccount')">
            <p>{{ t('delteAccountConfirm') }}</p>
            <template #action>
                <n-button :loading="loading" @click="deleteAccount" size="small" tertiary type="error">
                    {{ t('delteAccount') }}
                </n-button>
            </template>
        </n-modal>
    </div>
</template>

<style scoped>
.center {
    display: flex;
    justify-content: center;
}


.n-card {
    max-width: 800px;
    text-align: left;
}

.n-button {
    margin-top: 10px;
}
</style>
