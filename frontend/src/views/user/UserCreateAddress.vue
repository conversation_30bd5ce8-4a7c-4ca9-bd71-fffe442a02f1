<script setup>
import { ref, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { useMessage, useNotification } from 'naive-ui'
import { NewLabelOutlined } from '@vicons/material'

import Turnstile from '../../components/Turnstile.vue'
import { useGlobalState } from '../../store'
import { api } from '../../api'
import { getRouterPathWithLang } from '../../utils'

const message = useMessage()
const notification = useNotification()
const router = useRouter()

const {
    loading, openSettings, userSettings
} = useGlobalState()

const emailName = ref("")
const emailDomain = ref("")
const cfToken = ref("")
const hasAddress = ref(false)
const userAddress = ref("")

const { locale, t } = useI18n({
    messages: {
        en: {
            createNewEmail: 'Create New Email',
            createNewEmailTip1: 'You can only create one email address per account.',
            createNewEmailTip2: 'The email will be permanently bound to your account.',
            createNewEmailTip3: 'You cannot transfer or unbind the email address.',
            generateName: 'Generate Random Name',
            emailCreated: 'Email Created Successfully',
            alreadyHasEmail: 'You already have an email address',
            currentEmail: 'Your current email address',
            emailBoundPermanently: 'This email is permanently bound to your account',
            fillInAllFields: 'Please fill in all fields',
            address: 'Email Address',
        },
        zh: {
            createNewEmail: '创建新邮箱',
            createNewEmailTip1: '每个账户只能创建一个邮箱地址。',
            createNewEmailTip2: '邮箱将永久绑定到您的账户。',
            createNewEmailTip3: '您无法转移或解绑邮箱地址。',
            generateName: '生成随机名称',
            emailCreated: '邮箱创建成功',
            alreadyHasEmail: '您已经拥有邮箱地址',
            currentEmail: '您当前的邮箱地址',
            emailBoundPermanently: '此邮箱已永久绑定到您的账户',
            fillInAllFields: '请填写所有字段',
            address: '邮箱地址',
        }
    }
});

const generateName = async () => {
    try {
        const randomName = Math.random().toString(36).substring(2, 15);
        emailName.value = randomName;
    } catch (error) {
        message.error(error.message || "error");
    }
};

const createEmail = async () => {
    if (!emailName.value || !emailDomain.value) {
        message.error(t('fillInAllFields'));
        return;
    }
    
    try {
        const res = await api.fetch("/user_api/create_address", {
            method: "POST",
            body: JSON.stringify({
                name: emailName.value,
                domain: emailDomain.value,
                cf_token: cfToken.value,
            }),
        });
        
        message.success(t('emailCreated'));
        // 刷新页面数据
        await checkUserAddress();
        // 清空表单
        emailName.value = "";
        cfToken.value = "";
    } catch (error) {
        message.error(error.message || "error");
    }
};

const checkUserAddress = async () => {
    try {
        const { results } = await api.fetch("/user_api/bind_address");
        if (results && results.length > 0) {
            hasAddress.value = true;
            userAddress.value = results[0].name;
        } else {
            hasAddress.value = false;
            userAddress.value = "";
        }
    } catch (error) {
        console.error("检查用户邮箱失败:", error);
    }
};

const addressPrefix = computed(() => {
    // if user has role, return role prefix
    if (userSettings.value?.user_role) {
        return userSettings.value.user_role.prefix || "";
    }
    // if user has no role, return default prefix
    return openSettings.value.prefix;
});

const domainsOptions = computed(() => {
    // if user has role, return role domains
    if (userSettings.value.user_role) {
        const allDomains = userSettings.value.user_role.domains;
        if (!allDomains) return openSettings.value.domains;
        return openSettings.value.domains.filter((domain) => {
            return allDomains.includes(domain.value);
        });
    }
    // if user has no role, return default domains
    if (!openSettings.value.defaultDomains) {
        return openSettings.value.domains;
    }
    // if user has no role and no default domains, return all domains
    return openSettings.value.domains.filter((domain) => {
        return openSettings.value.defaultDomains.includes(domain.value);
    });
});

onMounted(async () => {
    if (!openSettings.value.domains || openSettings.value.domains.length === 0) {
        await api.getOpenSettings(message, notification);
    }
    emailDomain.value = domainsOptions.value ? domainsOptions.value[0]?.value : "";
    await checkUserAddress();
});
</script>

<template>
    <div>
        <div v-if="hasAddress">
            <n-alert type="info" :show-icon="false" :bordered="false">
                <div>
                    <p><strong>{{ t('alreadyHasEmail') }}</strong></p>
                    <p>{{ t('currentEmail') }}: <code>{{ userAddress }}</code></p>
                    <p style="color: #999; font-size: 12px;">{{ t('emailBoundPermanently') }}</p>
                </div>
            </n-alert>
        </div>
        
        <div v-else>
            <n-alert type="warning" :show-icon="false" :bordered="false" style="margin-bottom: 20px;">
                <div>
                    <p>{{ t('createNewEmailTip1') }}</p>
                    <p>{{ t('createNewEmailTip2') }}</p>
                    <p>{{ t('createNewEmailTip3') }}</p>
                </div>
            </n-alert>
            
            <n-form>
                <n-button @click="generateName" style="margin-bottom: 10px;">
                    {{ t('generateName') }}
                </n-button>
                
                <n-form-item-row :label="t('address')" required>
                    <n-input-group>
                        <n-input-group-label v-if="addressPrefix">
                            {{ addressPrefix }}
                        </n-input-group-label>
                        <n-input 
                            v-model:value="emailName" 
                            show-count 
                            :minlength="openSettings.minAddressLen"
                            :maxlength="openSettings.maxAddressLen" 
                        />
                        <n-input-group-label>@</n-input-group-label>
                        <n-select 
                            v-model:value="emailDomain" 
                            :consistent-menu-width="false"
                            :options="domainsOptions" 
                        />
                    </n-input-group>
                </n-form-item-row>
                
                <Turnstile v-model:value="cfToken" />
                
                <n-button 
                    type="primary" 
                    block 
                    secondary 
                    strong 
                    @click="createEmail" 
                    :loading="loading"
                >
                    <template #icon>
                        <n-icon :component="NewLabelOutlined" />
                    </template>
                    {{ t('createNewEmail') }}
                </n-button>
            </n-form>
        </div>
    </div>
</template>

<style scoped>
.n-form .n-button {
    margin-top: 10px;
}

.n-form {
    text-align: left;
}
</style>
