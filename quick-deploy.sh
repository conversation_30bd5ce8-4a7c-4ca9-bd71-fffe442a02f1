#!/bin/bash

# 快速部署脚本
# 使用方法: ./quick-deploy.sh

set -e

echo "🚀 Cloudflare Temp Email 快速部署"
echo "================================="

# 检查是否在正确的目录
if [ ! -f "worker/package.json" ] || [ ! -f "frontend/package.json" ]; then
    echo "❌ 请在项目根目录运行此脚本"
    exit 1
fi

# 检查 Cloudflare 认证
echo "🔐 检查 Cloudflare 认证..."
if ! wrangler whoami &> /dev/null; then
    echo "❌ 未登录 Cloudflare，请先运行: wrangler login"
    exit 1
fi

echo "✅ Cloudflare 认证成功"

# 检查配置文件
if [ ! -f "worker/wrangler.toml" ]; then
    echo "❌ 未找到 worker/wrangler.toml 配置文件"
    echo "请先根据 DEPLOYMENT_GUIDE.md 创建配置文件"
    exit 1
fi

# 读取配置
echo "📋 读取配置..."
worker_name=$(grep "^name" worker/wrangler.toml | sed 's/name = "\(.*\)"/\1/')
echo "Worker 名称: $worker_name"

# 部署 Worker
echo "🔨 部署 Worker..."
cd worker/
echo "📦 安装依赖..."
pnpm install --no-frozen-lockfile

echo "🚀 部署到 Cloudflare Workers..."
if pnpm run deploy; then
    echo "✅ Worker 部署成功"
    worker_url="https://$worker_name.$(wrangler whoami | grep "Account ID" | awk '{print $3}').workers.dev"
    echo "Worker URL: $worker_url"
else
    echo "❌ Worker 部署失败"
    exit 1
fi

cd ..

# 部署前端
echo "🌐 部署前端..."
cd frontend/

echo "📦 安装依赖..."
pnpm install --no-frozen-lockfile

echo "🏗️  构建前端..."
pnpm run build:pages

# 获取或创建 Pages 项目名称
read -p "请输入 Pages 项目名称 (默认: temp-email-frontend): " pages_name
pages_name=${pages_name:-temp-email-frontend}

echo "🚀 部署到 Cloudflare Pages..."
if wrangler pages deploy ./dist --project-name="$pages_name" --branch=production; then
    echo "✅ 前端部署成功"
    pages_url="https://$pages_name.pages.dev"
    echo "Pages URL: $pages_url"
else
    echo "❌ 前端部署失败"
    exit 1
fi

cd ..

echo ""
echo "🎉 部署完成！"
echo "================================="
echo "📋 部署信息:"
echo "  - Worker: $worker_url"
echo "  - 前端: $pages_url"
echo ""
echo "🔧 下一步:"
echo "1. 访问管理员界面: $pages_url/admin"
echo "2. 配置 Linux.do OAuth2 (参考 LINUX_DO_OAUTH2_SETUP.md)"
echo "3. 设置自定义域名 (可选)"
echo "4. 测试登录功能"
echo ""
echo "📖 详细配置请查看 DEPLOYMENT_GUIDE.md"
