#!/usr/bin/env node

/**
 * Linux.do OAuth2 配置脚本
 * 
 * 使用方法：
 * 1. 确保你的应用已经部署并可以访问
 * 2. 运行此脚本来生成 OAuth2 配置
 * 3. 将配置复制到管理员界面中
 */

const readline = require('readline');

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

function question(prompt) {
    return new Promise((resolve) => {
        rl.question(prompt, resolve);
    });
}

async function main() {
    console.log('=== Linux.do OAuth2 配置助手 ===\n');
    
    // 获取用户输入
    const domain = await question('请输入你的应用域名 (例如: https://your-app.example.com): ');
    const clientId = await question('请输入 Linux.do Client ID: ');
    const clientSecret = await question('请输入 Linux.do Client Secret: ');
    
    // 生成配置
    const config = {
        name: "Linux.do",
        clientID: clientId.trim(),
        clientSecret: clientSecret.trim(),
        authorizationURL: "https://connect.linux.do/oauth2/authorize",
        accessTokenURL: "https://connect.linux.do/oauth2/token",
        accessTokenFormat: "urlencoded",
        userInfoURL: "https://connect.linux.do/api/user",
        redirectURL: `${domain.trim().replace(/\/$/, '')}/user/oauth2/callback`,
        logoutURL: "",
        userEmailKey: "username",
        scope: "read",
        enableMailAllowList: false,
        mailAllowList: ["linux.do"]
    };
    
    console.log('\n=== 生成的配置 ===');
    console.log(JSON.stringify(config, null, 2));
    
    console.log('\n=== 配置说明 ===');
    console.log('1. 复制上面的 JSON 配置');
    console.log('2. 登录到你的应用管理员界面');
    console.log('3. 进入 "用户 OAuth2 设置" 页面');
    console.log('4. 点击 "添加 Oauth2"，选择 "Linux.do" 类型');
    console.log('5. 填入对应的配置信息');
    console.log('6. 保存配置');
    
    console.log('\n=== 重要提醒 ===');
    console.log(`请确保在 Linux.do 应用设置中配置正确的回调 URL: ${config.redirectURL}`);
    console.log('Linux.do 用户将使用虚拟邮箱地址 <EMAIL> 进行登录');
    
    rl.close();
}

main().catch(console.error);
