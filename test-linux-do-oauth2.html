<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Linux.do OAuth2 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .config-box {
            background: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            overflow-x: auto;
        }
        .step {
            background: #e8f4fd;
            border-left: 4px solid #2196F3;
            padding: 15px;
            margin: 15px 0;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        code {
            background: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        pre {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            overflow-x: auto;
        }
        .test-url {
            background: #fff;
            border: 2px solid #007bff;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            text-align: center;
        }
        .test-url a {
            color: #007bff;
            text-decoration: none;
            font-weight: bold;
            font-size: 18px;
        }
        .test-url a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <h1>Linux.do OAuth2 配置测试</h1>
    
    <div class="success">
        <h3>✅ 配置已完成</h3>
        <p>根据你提供的信息，Linux.do OAuth2 配置已经准备就绪。</p>
    </div>

    <h2>📋 配置信息</h2>
    <div class="config-box">
        <h4>Linux.do OAuth2 配置：</h4>
        <pre><code>{
  "name": "Linux.do",
  "clientID": "********************************",
  "clientSecret": "qxXrcbSOvAyQ2mQvGYP6joYI7bDzsqWE",
  "authorizationURL": "https://connect.linux.do/oauth2/authorize",
  "accessTokenURL": "https://connect.linux.do/oauth2/token",
  "accessTokenFormat": "urlencoded",
  "userInfoURL": "https://connect.linux.do/api/user",
  "redirectURL": "https://tmail.ui.edu.kg/user/oauth2/callback",
  "userEmailKey": "username",
  "scope": "read",
  "enableMailAllowList": false,
  "mailAllowList": ["linux.do"]
}</code></pre>
    </div>

    <h2>🚀 配置步骤</h2>
    
    <div class="step">
        <h4>步骤 1: 访问管理员界面</h4>
        <p>访问: <code>https://tmail.ui.edu.kg/admin</code></p>
    </div>

    <div class="step">
        <h4>步骤 2: 进入 OAuth2 设置</h4>
        <p>在管理员界面中找到 "用户 OAuth2 设置" 页面</p>
    </div>

    <div class="step">
        <h4>步骤 3: 添加 Linux.do 配置</h4>
        <p>1. 点击 "添加 Oauth2" 按钮</p>
        <p>2. 选择 "Linux.do" 类型</p>
        <p>3. 填入上面的配置信息</p>
        <p>4. 点击 "保存" 按钮</p>
    </div>

    <div class="step">
        <h4>步骤 4: 验证回调 URL</h4>
        <p>确保在 Linux.do 应用设置中配置了正确的回调 URL：</p>
        <code>https://tmail.ui.edu.kg/user/oauth2/callback</code>
    </div>

    <h2>🧪 测试登录</h2>
    
    <div class="test-url">
        <p>配置完成后，可以通过以下链接测试 Linux.do 登录：</p>
        <a href="https://tmail.ui.edu.kg/user" target="_blank">
            测试 Linux.do OAuth2 登录
        </a>
    </div>

    <div class="warning">
        <h4>⚠️ 重要提醒</h4>
        <ul>
            <li>Linux.do 用户将使用虚拟邮箱地址 <code><EMAIL></code> 进行登录</li>
            <li>确保回调 URL 在 Linux.do 应用设置中配置正确</li>
            <li>首次登录会自动创建用户账号</li>
            <li>用户可以通过 "忘记密码" 功能为账号设置密码</li>
        </ul>
    </div>

    <h2>🔧 故障排除</h2>
    
    <div class="step">
        <h4>常见问题</h4>
        <ul>
            <li><strong>回调 URL 不匹配</strong>: 检查 Linux.do 应用设置中的回调 URL</li>
            <li><strong>Client ID/Secret 错误</strong>: 验证配置中的凭据是否正确</li>
            <li><strong>用户无法登录</strong>: 检查浏览器开发者工具的网络请求</li>
            <li><strong>权限不足</strong>: 确认用户有足够的权限访问应用</li>
        </ul>
    </div>

    <h2>📚 技术说明</h2>
    
    <div class="config-box">
        <h4>用户信息映射：</h4>
        <ul>
            <li><code>id</code>: 用户唯一标识符（不会改变）</li>
            <li><code>username</code>: 用户名（可能会改变）→ 映射为邮箱前缀</li>
            <li><code>name</code>: 用户昵称</li>
            <li><code>trust_level</code>: 信任等级（0-4）</li>
        </ul>
        
        <h4>虚拟邮箱生成规则：</h4>
        <p><code>用户名@linux.do</code></p>
        <p>例如：用户名 <code>testuser</code> → 邮箱 <code><EMAIL></code></p>
    </div>

    <div class="success">
        <h4>✨ 配置完成</h4>
        <p>按照上述步骤完成配置后，你的应用就可以支持 Linux.do OAuth2 登录了！</p>
    </div>
</body>
</html>
