import { Context } from "hono";
import { getBooleanValue } from "../utils";


export default {
    getAutoReply: async (c: Context<HonoCustomType>) => {
        // JWT邮箱凭证系统已被禁用，请使用用户登录访问邮箱
        return c.text("邮箱凭证系统已被禁用，请登录后使用用户API访问自动回复设置", 403)
    },
    saveAutoReply: async (c: Context<HonoCustomType>) => {
        // JWT邮箱凭证系统已被禁用，请使用用户登录访问邮箱
        return c.text("邮箱凭证系统已被禁用，请登录后使用用户API保存自动回复设置", 403)
    }
}
