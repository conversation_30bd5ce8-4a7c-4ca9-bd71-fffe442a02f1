import { Hono } from 'hono'

import i18n from '../i18n';
import { getBooleanValue, getJsonSetting, checkCfTurnstile, getStringValue, getSplitStringListValue } from '../utils';
import { newAddress, handleListQuery, deleteAddressWithData, getAddressPrefix, getAllowDomains } from '../common'
import { CONSTANTS } from '../constants'
import auto_reply from './auto_reply'
import webhook_settings from './webhook_settings';
import s3_attachment from './s3_attachment';

export const api = new Hono<HonoCustomType>()

api.get('/api/auto_reply', auto_reply.getAutoReply)
api.post('/api/auto_reply', auto_reply.saveAutoReply)
api.get('/api/webhook/settings', webhook_settings.getWebhookSettings)
api.post('/api/webhook/settings', webhook_settings.saveWebhookSettings)
api.post('/api/webhook/test', webhook_settings.testWebhookSettings)
api.get('/api/attachment/list', s3_attachment.list)
api.post('/api/attachment/delete', s3_attachment.deleteKey)
api.post('/api/attachment/put_url', s3_attachment.getSignedPutUrl)
api.post('/api/attachment/get_url', s3_attachment.getSignedGetUrl)

api.get('/api/mails', async (c) => {
    // JWT邮箱凭证系统已被禁用，请使用用户登录访问邮箱
    return c.text("邮箱凭证系统已被禁用，请登录后使用 /user_api/mails 访问邮箱", 403)
})

api.get('/api/mail/:mail_id', async (c) => {
    // JWT邮箱凭证系统已被禁用，请使用用户登录访问邮箱
    return c.text("邮箱凭证系统已被禁用，请登录后使用用户API访问邮箱", 403)
})

api.delete('/api/mails/:id', async (c) => {
    // JWT邮箱凭证系统已被禁用，请使用用户登录访问邮箱
    return c.text("邮箱凭证系统已被禁用，请登录后使用 /user_api/mails 删除邮件", 403)
})

api.get('/api/settings', async (c) => {
    // JWT邮箱凭证系统已被禁用，请使用用户登录访问邮箱
    return c.text("邮箱凭证系统已被禁用，请登录后使用用户API访问邮箱设置", 403)
})

api.post('/api/new_address', async (c) => {
    // 匿名用户创建邮箱功能已被禁用，请登录后使用用户API创建邮箱
    // 每个用户只能创建一个永久绑定的邮箱
    return c.text("匿名用户创建邮箱功能已被禁用，请登录后使用 /user_api/create_address 创建邮箱", 403)
})

api.delete('/api/delete_address', async (c) => {
    // JWT邮箱凭证系统已被禁用，邮箱已永久绑定到用户账户，无法删除
    return c.text("邮箱已永久绑定到用户账户，无法删除", 403)
})
