import { Context } from "hono";
import {
    S3Client,
    ListObjectsV2Command,
    GetObjectCommand,
    PutObjectCommand,
    DeleteObjectCommand
} from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";

export const isS3Enabled = (c: Context<HonoCustomType>) => {
    return !(!c.env.S3_ENDPOINT ||
        !c.env.S3_ACCESS_KEY_ID ||
        !c.env.S3_SECRET_ACCESS_KEY ||
        !c.env.S3_BUCKET);
}

const getS3Client = (c: Context<HonoCustomType>) => {
    if (
        !c.env.S3_ENDPOINT ||
        !c.env.S3_ACCESS_KEY_ID ||
        !c.env.S3_SECRET_ACCESS_KEY ||
        !c.env.S3_BUCKET
    ) {
        throw new Error("S3 config is not set");
    }
    return new S3Client({
        region: "auto",
        endpoint: c.env.S3_ENDPOINT,
        credentials: {
            accessKeyId: c.env.S3_ACCESS_KEY_ID,
            secretAccessKey: c.env.S3_SECRET_ACCESS_KEY,
        },
    });
}

export default {
    getSignedGetUrl: async (c: Context<HonoCustomType>) => {
        // JWT邮箱凭证系统已被禁用，请使用用户登录访问邮箱
        return c.text("邮箱凭证系统已被禁用，请登录后使用用户API访问S3附件", 403)
    },
    getSignedPutUrl: async (c: Context<HonoCustomType>) => {
        // JWT邮箱凭证系统已被禁用，请使用用户登录访问邮箱
        return c.text("邮箱凭证系统已被禁用，请登录后使用用户API上传S3附件", 403)
    },
    list: async (c: Context<HonoCustomType>) => {
        // JWT邮箱凭证系统已被禁用，请使用用户登录访问邮箱
        return c.text("邮箱凭证系统已被禁用，请登录后使用用户API查看S3附件", 403)
    },
    deleteKey: async (c: Context<HonoCustomType>) => {
        // JWT邮箱凭证系统已被禁用，请使用用户登录访问邮箱
        return c.text("邮箱凭证系统已被禁用，请登录后使用用户API删除S3附件", 403)
    }
}
