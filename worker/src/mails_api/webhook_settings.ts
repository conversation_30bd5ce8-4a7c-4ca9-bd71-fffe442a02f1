import { Context } from "hono";
import { CONSTANTS } from "../constants";
import { AdminWebhookSettings, WebhookSettings } from "../models";
import { commonParseMail, sendWebhook } from "../common";


async function getWebhookSettings(c: Context<HonoCustomType>): Promise<Response> {
    // JWT邮箱凭证系统已被禁用，请使用用户登录访问邮箱
    return c.text("邮箱凭证系统已被禁用，请登录后使用用户API访问Webhook设置", 403)
}


async function saveWebhookSettings(c: Context<HonoCustomType>): Promise<Response> {
    // JWT邮箱凭证系统已被禁用，请使用用户登录访问邮箱
    return c.text("邮箱凭证系统已被禁用，请登录后使用用户API保存Webhook设置", 403)
}

async function testWebhookSettings(c: Context<HonoCustomType>): Promise<Response> {
    // JWT邮箱凭证系统已被禁用，请使用用户登录访问邮箱
    return c.text("邮箱凭证系统已被禁用，请登录后使用用户API测试Webhook设置", 403)
}

export default {
    getWebhookSettings,
    saveWebhookSettings,
    testWebhookSettings,
}
