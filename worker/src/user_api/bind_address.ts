import { Context } from 'hono';

import { UserSettings } from "../models";
import { getJsonSetting, checkCfTurnstile, getIntValue } from "../utils"
import { newAddress, getAddressPrefix } from "../common";
import { CONSTANTS } from "../constants";
import i18n from '../i18n';

const UserBindAddressModule = {
    bind: async (c: Context<HonoCustomType>) => {
        const { user_id } = c.get("userPayload");
        const { address_id } = c.get("jwtPayload");
        return await UserBindAddressModule.bindByID(c, user_id, address_id)
    },
    bindByID: async (
        c: Context<HonoCustomType>,
        user_id: number | string, address_id: number | string
    ) => {
        if (!address_id || !user_id) {
            return c.text("No address or user token", 400)
        }
        // check if address exists
        const db_address_id = await c.env.DB.prepare(
            `SELECT id FROM address where id = ?`
        ).bind(address_id).first("id");
        if (!db_address_id) {
            return c.text("Address not found", 400)
        }
        // check if user exists
        const db_user_id = await c.env.DB.prepare(
            `SELECT id FROM users where id = ?`
        ).bind(user_id).first("id");
        if (!db_user_id) {
            return c.text("User not found", 400)
        }
        // check if binded
        const db_user_address_id = await c.env.DB.prepare(
            `SELECT user_id FROM users_address where user_id = ? and address_id = ?`
        ).bind(user_id, address_id).first("user_id");
        if (db_user_address_id) return c.json({ success: true })
        // check if binded address count
        const value = await getJsonSetting(c, CONSTANTS.USER_SETTINGS_KEY);
        const settings = new UserSettings(value);
        if (settings.maxAddressCount > 0) {
            const { count } = await c.env.DB.prepare(
                `SELECT COUNT(*) as count FROM users_address where user_id = ?`
            ).bind(user_id).first<{ count: number }>() || { count: 0 };
            if (count >= settings.maxAddressCount) {
                return c.text("Max address count reached", 400)
            }
        }
        // bind
        try {
            const { success } = await c.env.DB.prepare(
                `INSERT INTO users_address (user_id, address_id) VALUES (?, ?)`
            ).bind(user_id, address_id).run();
            if (!success) {
                return c.text("Failed to bind", 500)
            }
        } catch (e) {
            const error = e as Error;
            if (error.message && error.message.includes("UNIQUE")) {
                return c.text("Address already binded, please unbind first", 400)
            }
            return c.text("Failed to bind", 500)
        }
        return c.json({ success: true })
    },
    unbind: async (c: Context<HonoCustomType>) => {
        // 用户邮箱永久绑定，禁用解绑功能
        return c.text("邮箱已永久绑定到您的账户，无法解绑", 403);
    },
    getBindedAddresses: async (c: Context<HonoCustomType>) => {
        const { user_id } = c.get("userPayload");
        const results = await UserBindAddressModule.getBindedAddressesById(c, user_id);
        return c.json({
            results: results,
        });
    },
    getBindedAddressListById: async (
        c: Context<HonoCustomType>, user_id: number | string
    ): Promise<string[]> => {
        const bindedAddressList = await UserBindAddressModule.getBindedAddressesById(c, user_id);
        return bindedAddressList.map((item) => item.name);
    },
    getBindedAddressesById: async (
        c: Context<HonoCustomType>, user_id: number | string
    ): Promise<{
        id: number;
        name: string;
        mail_count: number;
        send_count: number;
        created_at: string;
        updated_at: string;
    }[]> => {
        const msgs = i18n.getMessagesbyContext(c);
        if (!user_id) {
            throw new Error(msgs.UserNotFoundMsg);
        }
        // select binded address
        const { results } = await c.env.DB.prepare(
            `SELECT a.*,`
            + ` (SELECT COUNT(*) FROM raw_mails WHERE address = a.name) AS mail_count,`
            + ` (SELECT COUNT(*) FROM sendbox WHERE address = a.name) AS send_count`
            + ` FROM address a `
            + ` JOIN users_address ua `
            + ` ON ua.address_id = a.id `
            + ` WHERE ua.user_id = ?`
            + ` ORDER BY a.id DESC`
        ).bind(user_id).all<{
            id: number;
            name: string;
            mail_count: number;
            send_count: number;
            created_at: string;
            updated_at: string;
        }>();
        return results || [];
    },
    getBindedAddressJwt: async (c: Context<HonoCustomType>) => {
        // JWT邮箱凭证系统已被移除，不再生成邮箱凭证
        // 所有邮箱访问现在都需要用户登录验证
        return c.text("邮箱凭证系统已被禁用，请使用用户登录访问邮箱", 403)
    },
    transferAddress: async (c: Context<HonoCustomType>) => {
        // 用户邮箱永久绑定，禁用转移功能
        return c.text("邮箱已永久绑定到您的账户，无法转移", 403);
    },
    createUserAddress: async (c: Context<HonoCustomType>) => {
        const { user_id } = c.get("userPayload");
        const lang = c.get("lang") || c.env.DEFAULT_LANG;
        const msgs = i18n.getMessages(lang);

        // 检查用户是否已经有邮箱
        const existingAddresses = await UserBindAddressModule.getBindedAddressesById(c, user_id);
        if (existingAddresses.length > 0) {
            return c.text("用户已经拥有邮箱，每个用户只能创建一个邮箱", 400);
        }

        // 检查系统邮箱总数限制
        const maxTotalAddresses = getIntValue(c.env.MAX_TOTAL_ADDRESSES, 0);
        if (maxTotalAddresses > 0) {
            const { count: totalAddresses } = await c.env.DB.prepare(
                `SELECT COUNT(*) as count FROM address`
            ).first<{ count: number }>() || { count: 0 };

            if (totalAddresses >= maxTotalAddresses) {
                return c.text(`系统邮箱数量已达上限 (${maxTotalAddresses})，请等待下次注册名额开放`, 429);
            }
        }

        const { name, domain, cf_token } = await c.req.json();

        // 检查 Cloudflare Turnstile
        try {
            await checkCfTurnstile(c, cf_token);
        } catch (error) {
            return c.text(msgs.TurnstileCheckFailedMsg, 500);
        }

        // 检查名称黑名单
        try {
            const value = await getJsonSetting(c, CONSTANTS.ADDRESS_BLOCK_LIST_KEY);
            const blockList = (value || []) as string[];
            if (blockList.some((item) => name.includes(item))) {
                return c.text(`Name[${name}] is blocked`, 400);
            }
        } catch (error) {
            console.error(error);
        }

        try {
            const addressPrefix = await getAddressPrefix(c);
            const addressResult = await newAddress(c, {
                name,
                domain,
                enablePrefix: true,
                checkLengthByConfig: true,
                addressPrefix
            });

            // 获取创建的邮箱 ID
            const address_id = await c.env.DB.prepare(
                `SELECT id FROM address WHERE name = ?`
            ).bind(addressResult.address).first<number>("id");

            if (!address_id) {
                throw new Error("无法找到创建的邮箱 ID");
            }

            // 自动绑定到用户账户
            await UserBindAddressModule.bindByID(c, user_id, address_id);

            // 返回邮箱地址而不是 JWT
            return c.json({
                success: true,
                address: addressResult.address,
                message: "邮箱创建成功并已绑定到您的账户"
            });
        } catch (e) {
            return c.text(`创建邮箱失败: ${(e as Error).message}`, 400);
        }
    }
}

export default UserBindAddressModule;
