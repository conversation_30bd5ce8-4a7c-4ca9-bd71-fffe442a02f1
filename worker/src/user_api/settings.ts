import { Context } from "hono";

import i18n from "../i18n";
import { UserOauth2Settings, UserSettings } from "../models";
import { getJsonSetting, getUserRoles } from "../utils"
import { CONSTANTS } from "../constants";
import { commonGetUserRole } from "../common";
import { Jwt } from "hono/utils/jwt";
import UserBindAddressModule from "./bind_address";

export default {
    openSettings: async (c: Context<HonoCustomType>) => {
        const value = await getJsonSetting(c, CONSTANTS.USER_SETTINGS_KEY);
        const settings = new UserSettings(value);
        const oauth2ClientIDs = [] as { clientID: string, name: string }[];
        try {
            const oauth2Settings = await getJsonSetting<UserOauth2Settings[]>(c, CONSTANTS.OAUTH2_SETTINGS_KEY);
            oauth2ClientIDs.push(
                ...oauth2Settings?.map(s => ({
                    clientID: s.clientID,
                    name: s.name
                })) || []
            );
        } catch (e) {
            console.error("Failed to get oauth2 settings", e);
        }
        return c.json({
            enable: settings.enable,
            enableMailVerify: settings.enableMailVerify,
            oauth2ClientIDs: oauth2ClientIDs,
        })
    },
    settings: async (c: Context<HonoCustomType>) => {
        const user = c.get("userPayload");
        const lang = c.get("lang") || c.env.DEFAULT_LANG;
        const msgs = i18n.getMessages(lang);
        // check if user exists
        const db_user_id = await c.env.DB.prepare(
            `SELECT id FROM users where id = ?`
        ).bind(user.user_id).first<number | undefined | null>("id");
        if (!db_user_id) {
            return c.text(msgs.UserNotFoundMsg, 400);
        }
        const user_role = await commonGetUserRole(c, db_user_id);
        const is_admin = (
            c.env.ADMIN_USER_ROLE
            &&
            c.env.ADMIN_USER_ROLE === user_role?.role
        );
        const access_token = user_role?.role ? await Jwt.sign({
            user_email: user.user_email,
            user_id: user.user_id,
            user_role: user_role.role,
            iat: Math.floor(Date.now() / 1000),
            // 1 hour
            exp: Math.floor(Date.now() / 1000) + 3600,
        }, c.env.JWT_SECRET, "HS256") : null;
        // create new if expired in 7 days
        const new_user_token = user.exp > (
            Math.floor(Date.now() / 1000) + 7 * 24 * 60 * 60
        ) ? null : await Jwt.sign({
            user_email: user.user_email,
            user_id: user.user_id,
            // 30 days expire in seconds
            exp: Math.floor(Date.now() / 1000) + 30 * 24 * 60 * 60,
            iat: Math.floor(Date.now() / 1000),
        }, c.env.JWT_SECRET, "HS256");

        // 获取用户绑定的邮箱地址
        const bindedAddresses = await UserBindAddressModule.getBindedAddressesById(c, db_user_id);

        // 获取用户当前选择的邮箱
        let currentAddress = "";

        if (bindedAddresses.length > 0) {
            // 先尝试从KV存储中获取用户选择的邮箱
            const userSelectedAddress = await c.env.KV.get(`user_selected_address:${db_user_id}`);

            if (userSelectedAddress && bindedAddresses.find(addr => addr.name === userSelectedAddress)) {
                currentAddress = userSelectedAddress;
            } else {
                // 如果没有存储的选择或选择的邮箱不存在，使用第一个邮箱
                currentAddress = bindedAddresses[0].name;
                // 保存默认选择到KV
                await c.env.KV.put(`user_selected_address:${db_user_id}`, currentAddress);
            }
        }

        return c.json({
            ...user,
            is_admin: is_admin,
            access_token: access_token,
            new_user_token: new_user_token,
            user_role: user_role,
            address: currentAddress,
            addresses: bindedAddresses, // 返回所有邮箱地址
            send_balance: 0, // TODO: 实现发送余额查询
            auto_reply: {} // TODO: 实现自动回复设置查询
        });
    },
    switchAddress: async (c: Context<HonoCustomType>) => {
        const user = c.get("userPayload");
        const { address } = await c.req.json();

        if (!address || typeof address !== 'string') {
            return c.text("Valid address is required", 400);
        }

        // 输入验证：邮箱地址格式检查
        const { validateEmail } = await import('../utils');
        if (!validateEmail(address)) {
            return c.text("Invalid email address format", 400);
        }

        // 验证用户是否存在
        const db_user_id = await c.env.DB.prepare(
            `SELECT id FROM users where id = ?`
        ).bind(user.user_id).first<number | undefined | null>("id");

        if (!db_user_id) {
            return c.text("User not found", 400);
        }

        // 速率限制：防止频繁切换邮箱
        const rateLimitKey = `switch_address_rate_limit:${db_user_id}`;
        const lastSwitch = await c.env.KV.get(rateLimitKey);
        const now = Date.now();

        if (lastSwitch) {
            const timeDiff = now - parseInt(lastSwitch);
            const minInterval = 5000; // 5秒间隔

            if (timeDiff < minInterval) {
                return c.text(`请等待 ${Math.ceil((minInterval - timeDiff) / 1000)} 秒后再切换邮箱`, 429);
            }
        }

        // 获取用户绑定的邮箱列表
        const bindedAddresses = await UserBindAddressModule.getBindedAddressesById(c, db_user_id);

        // 安全检查：确保用户拥有该邮箱
        const hasAddress = bindedAddresses.find(addr => addr.name === address);
        if (!hasAddress) {
            return c.text("Address not found or not owned by user", 403);
        }

        // 额外安全检查：确保邮箱在数据库中存在且有效
        const addressExists = await c.env.DB.prepare(
            `SELECT id FROM address WHERE name = ?`
        ).bind(address).first<number>("id");

        if (!addressExists) {
            return c.text("Address does not exist in system", 404);
        }

        // 保存用户选择的邮箱到KV存储
        await c.env.KV.put(`user_selected_address:${db_user_id}`, address);

        // 更新速率限制时间戳
        await c.env.KV.put(rateLimitKey, now.toString(), { expirationTtl: 60 }); // 1分钟过期

        return c.json({
            success: true,
            address: address,
            message: "Address switched successfully"
        });
    },
}
